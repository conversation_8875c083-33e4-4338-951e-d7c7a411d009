"""
Markdown生成模块
"""

from typing import List, Dict, Any
from utils.logger import get_logger
from config import MARKDOWN_LINE_BREAK

logger = get_logger(__name__)


class MarkdownGenerator:
    """Markdown生成器"""

    def __init__(self):
        """初始化Markdown生成器"""
        logger.info("Markdown生成器初始化完成")

    def generate_markdown(self, content_list: List[Dict[str, Any]]) -> str:
        """
        生成完整的Markdown文档

        Args:
            content_list: 处理后的内容列表

        Returns:
            Markdown字符串
        """
        logger.info(f"开始生成Markdown，包含 {len(content_list)} 个元素")

        markdown_parts = []

        for i, item in enumerate(content_list):
            try:
                if item.get("type") == "text":
                    md_content = self._process_text_item(item)
                elif item.get("type") == "table":
                    md_content = self._process_table_item(item)
                else:
                    logger.warning(f"未知的内容类型: {item.get('type')}")
                    continue

                if md_content:
                    markdown_parts.append(md_content)

            except Exception as e:
                logger.error(f"处理第 {i} 个元素时出错: {e}")
                continue

        # 拼接所有部分
        full_markdown = MARKDOWN_LINE_BREAK.join(markdown_parts)

        # 清理多余的空行
        full_markdown = self._clean_markdown(full_markdown)

        logger.info(f"Markdown生成完成，总长度: {len(full_markdown)} 字符")
        return full_markdown

    def _process_text_item(self, item: Dict[str, Any]) -> str:
        """
        处理文本项

        Args:
            item: 文本项数据

        Returns:
            Markdown格式的文本
        """
        text = item.get("text", "").strip()
        if not text:
            return ""

        # 检查是否有标题级别
        text_level = item.get("text_level")

        if text_level:
            # 生成标题
            if isinstance(text_level, int) and 1 <= text_level <= 6:
                header_prefix = "#" * text_level
                return f"{header_prefix} {text}"
            else:
                logger.warning(f"无效的标题级别: {text_level}")
                return text
        else:
            # 普通文本
            return text

    def _process_table_item(self, item: Dict[str, Any]) -> str:
        """
        处理表格项

        Args:
            item: 表格项数据

        Returns:
            Markdown格式的表格
        """
        parts = []

        # 添加表格标题
        table_caption = item.get("table_caption", [])
        if table_caption:
            caption_text = " ".join(table_caption).strip()
            if caption_text:
                parts.append(f"**{caption_text}**")

        # 处理表格主体 - 直接保留HTML格式
        table_body = item.get("table_body", "")
        if table_body:
            # 直接使用HTML表格，不转换为Markdown
            parts.append(table_body)

        # 添加表格脚注
        table_footnote = item.get("table_footnote", [])
        if table_footnote:
            footnote_text = " ".join(table_footnote).strip()
            if footnote_text:
                parts.append(f"*{footnote_text}*")

        return "\n\n".join(parts)

    def _clean_markdown(self, markdown: str) -> str:
        """
        清理Markdown内容

        Args:
            markdown: 原始Markdown字符串

        Returns:
            清理后的Markdown字符串
        """
        # 移除多余的空行（超过2个连续换行符）
        import re

        cleaned = re.sub(r"\n{3,}", "\n\n", markdown)

        # 移除行尾空格
        lines = cleaned.split("\n")
        cleaned_lines = [line.rstrip() for line in lines]

        # 移除开头和结尾的空行
        while cleaned_lines and not cleaned_lines[0].strip():
            cleaned_lines.pop(0)
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()

        return "\n".join(cleaned_lines)

    def generate_metadata_header(self, stats: Dict[str, Any], input_file: str) -> str:
        """
        生成文档元数据头部

        Args:
            stats: 处理统计信息
            input_file: 输入文件名

        Returns:
            元数据头部的Markdown
        """
        from datetime import datetime

        header_parts = [
            "---",
            f"# 文档处理报告",
            "",
            f"**源文件**: {input_file}",
            f"**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**页面范围**: {stats.get('page_range', 'N/A')}",
            f"**总页数**: {stats.get('total_pages', 0)}",
            "",
            "## 处理统计",
            f"- 原始元素数量: {stats.get('original_count', 0)}",
            f"- 处理后元素数量: {stats.get('processed_count', 0)}",
            f"- 合并的元素数量: {stats.get('elements_merged', 0)}",
            f"- 表格合并数量: {stats.get('tables_merged', 0)}",
            f"- 文本拼接数量: {stats.get('texts_joined', 0)}",
            "",
            "---",
            "",
        ]

        return "\n".join(header_parts)


# 创建全局Markdown生成器实例
markdown_generator = MarkdownGenerator()
