"""
表格合并模块
"""
from typing import Dict, Any, List
from bs4 import BeautifulSoup
from utils.logger import get_logger
from utils.html_utils import clean_html_table

logger = get_logger(__name__)


class TableMerger:
    """表格合并器"""
    
    def merge_tables_basic(self, prev_table: Dict[str, Any], next_table: Dict[str, Any]) -> Dict[str, Any]:
        """
        基础表格合并：简单拼接，不处理行合并
        
        Args:
            prev_table: 上一页的表格数据
            next_table: 下一页的表格数据
            
        Returns:
            合并后的表格数据
        """
        try:
            logger.info("执行基础表格合并")
            
            # 获取表格内容
            prev_body = prev_table.get('table_body', '')
            next_body = next_table.get('table_body', '')
            
            if not prev_body or not next_body:
                logger.warning("表格内容为空，跳过合并")
                return prev_table
            
            # 基础合并：保留下方表格的所有行（包括第一行）
            # 只有智能合并在LLM判断需要时才处理第一行的合并

            # 合并表格体
            merged_body = self._merge_table_bodies(prev_body, next_body)
            
            # 合并其他信息
            merged_table = {
                'type': 'table',
                'table_body': merged_body,
                'table_caption': prev_table.get('table_caption', []) + next_table.get('table_caption', []),
                'table_footnote': prev_table.get('table_footnote', []) + next_table.get('table_footnote', []),
                'img_path': prev_table.get('img_path', ''),  # 保留第一个表格的图片路径
                'page_idx': next_table.get('page_idx', 0)  # 使用第二个表格的页码，便于链式合并
            }
            
            logger.info("基础表格合并完成")
            return merged_table
            
        except Exception as e:
            logger.error(f"基础表格合并失败: {e}")
            # 返回原表格作为降级策略
            return prev_table
    
    def merge_tables_with_row_merge(self, prev_table: Dict[str, Any], next_table: Dict[str, Any], merge_first_row: bool = True) -> Dict[str, Any]:
        """
        智能表格合并：根据需要处理行合并

        Args:
            prev_table: 上一页的表格数据
            next_table: 下一页的表格数据
            merge_first_row: 是否合并下方表格的第一行

        Returns:
            合并后的表格数据
        """
        try:
            logger.info("执行智能表格合并（包含行合并）")
            
            # 解析HTML表格
            prev_rows = self._parse_table_rows(prev_table.get('table_body', ''))
            next_rows = self._parse_table_rows(next_table.get('table_body', ''))
            
            if not prev_rows or not next_rows:
                logger.warning("表格解析失败，使用基础合并")
                return self.merge_tables_basic(prev_table, next_table)
            
            # 根据参数决定是否合并第一行
            if merge_first_row:
                merged_rows = self._merge_last_and_first_row(prev_rows, next_rows)
            else:
                # 不合并第一行，直接拼接所有行
                merged_rows = prev_rows + next_rows
            
            # 重构HTML表格
            merged_body = self._reconstruct_table(merged_rows)
            
            # 合并其他信息
            merged_table = {
                'type': 'table',
                'table_body': merged_body,
                'table_caption': prev_table.get('table_caption', []) + next_table.get('table_caption', []),
                'table_footnote': prev_table.get('table_footnote', []) + next_table.get('table_footnote', []),
                'img_path': prev_table.get('img_path', ''),
                'page_idx': next_table.get('page_idx', 0)  # 使用第二个表格的页码，便于链式合并
            }
            
            logger.info("智能表格合并完成")
            return merged_table
            
        except Exception as e:
            logger.error(f"智能表格合并失败: {e}")
            # 降级到基础合并
            return self.merge_tables_basic(prev_table, next_table)
    
    def _merge_table_bodies(self, prev_body: str, next_body: str) -> str:
        """
        合并两个表格体的HTML
        
        Args:
            prev_body: 上一页表格体HTML
            next_body: 下一页表格体HTML
            
        Returns:
            合并后的HTML
        """
        try:
            # 解析HTML
            prev_soup = BeautifulSoup(prev_body, 'html.parser')
            next_soup = BeautifulSoup(next_body, 'html.parser')
            
            prev_table = prev_soup.find('table')
            next_table = next_soup.find('table')
            
            if not prev_table or not next_table:
                logger.warning("未找到table标签，直接拼接")
                return prev_body + '\n' + next_body
            
            # 将下一页的所有行添加到上一页表格中
            for tr in next_table.find_all('tr'):
                prev_table.append(tr)
            
            return str(prev_table)
            
        except Exception as e:
            logger.error(f"表格体合并失败: {e}")
            # 简单拼接作为降级策略
            return prev_body + '\n' + next_body
    
    def _parse_table_rows(self, html_table: str) -> List[List[Dict[str, Any]]]:
        """
        解析HTML表格为行数据
        
        Args:
            html_table: HTML表格字符串
            
        Returns:
            行数据列表
        """
        try:
            soup = BeautifulSoup(html_table, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return []
            
            rows = []
            for tr in table.find_all('tr'):
                row = []
                for cell in tr.find_all(['td', 'th']):
                    cell_data = {
                        'text': cell.get_text(strip=True),
                        'colspan': int(cell.get('colspan', 1)),
                        'rowspan': int(cell.get('rowspan', 1)),
                        'tag': cell.name,
                        'attributes': dict(cell.attrs)
                    }
                    row.append(cell_data)
                
                if row:  # 只添加非空行
                    rows.append(row)
            
            return rows
            
        except Exception as e:
            logger.error(f"表格行解析失败: {e}")
            return []
    
    def _merge_last_and_first_row(self, prev_rows: List[List[Dict[str, Any]]], 
                                  next_rows: List[List[Dict[str, Any]]]) -> List[List[Dict[str, Any]]]:
        """
        合并上一页的最后一行和下一页的第一行
        
        Args:
            prev_rows: 上一页的行数据
            next_rows: 下一页的行数据
            
        Returns:
            合并后的所有行数据
        """
        if not prev_rows or not next_rows:
            return prev_rows + next_rows
        
        # 获取要合并的行
        last_row = prev_rows[-1]
        first_row = next_rows[0]
        
        # 合并行
        merged_row = self._merge_two_rows(last_row, first_row)
        
        # 构建最终结果
        result = prev_rows[:-1] + [merged_row] + next_rows[1:]
        
        return result
    
    def _merge_two_rows(self, row1: List[Dict[str, Any]], row2: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        合并两行数据
        
        Args:
            row1: 第一行数据
            row2: 第二行数据
            
        Returns:
            合并后的行数据
        """
        merged_row = []
        max_len = max(len(row1), len(row2))
        
        for i in range(max_len):
            cell1 = row1[i] if i < len(row1) else {'text': '', 'colspan': 1, 'rowspan': 1, 'tag': 'td'}
            cell2 = row2[i] if i < len(row2) else {'text': '', 'colspan': 1, 'rowspan': 1, 'tag': 'td'}
            
            # 合并单元格内容
            merged_cell = cell1.copy()
            
            if not cell1['text'].strip() and cell2['text'].strip():
                # 第一行为空，使用第二行的内容
                merged_cell['text'] = cell2['text']
            elif cell1['text'].strip() and cell2['text'].strip():
                # 两行都有内容，拼接
                merged_cell['text'] = f"{cell1['text']} {cell2['text']}"
            # 如果第一行有内容而第二行为空，保持第一行的内容
            
            merged_row.append(merged_cell)
        
        return merged_row
    
    def _reconstruct_table(self, rows: List[List[Dict[str, Any]]]) -> str:
        """
        重构HTML表格
        
        Args:
            rows: 行数据
            
        Returns:
            HTML表格字符串
        """
        html_parts = ['<table>']
        
        for row in rows:
            html_parts.append('<tr>')
            for cell in row:
                # 构建单元格属性
                attrs = []
                if cell.get('colspan', 1) > 1:
                    attrs.append(f'colspan="{cell["colspan"]}"')
                if cell.get('rowspan', 1) > 1:
                    attrs.append(f'rowspan="{cell["rowspan"]}"')
                
                attr_str = ' ' + ' '.join(attrs) if attrs else ''
                tag = cell.get('tag', 'td')
                
                html_parts.append(f'<{tag}{attr_str}>{cell["text"]}</{tag}>')
            html_parts.append('</tr>')
        
        html_parts.append('</table>')
        
        return ''.join(html_parts)


# 创建全局表格合并器实例
table_merger = TableMerger()
